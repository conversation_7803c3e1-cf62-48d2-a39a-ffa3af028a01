import React, { useState, useEffect, useRef } from 'react';

// --- Configuration ---
const CLOUDFLARE_WORKER_URL = 'https://image-uploader.mauricio-e1e.workers.dev';
const CLOUDFLARE_AUTH_KEY = 'test-secret-key';
const GOOGLE_PLACES_API_KEY = 'AIzaSyB8yL3lXuLVfXxniT62ulMUhDWksTCscTE';

interface CustomFormProps {
  title?: string;
  webhookUrl?: string;
}

// Helper component for the upload icon
const UploadIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-10 w-10 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
);

// Helper component for the loading spinner
const Loader = () => (
    <div className="loader border-4 border-f3f3f3 border-t-4 border-t-blue-400 rounded-full w-6 h-6 animate-spin"></div>
);

const CustomForm: React.FC<CustomFormProps> = ({
  title = "Get Your Free Project Estimate",
  webhookUrl = "https://services.leadconnectorhq.com/hooks/BK5WOlszHMZB0udM7qC1/webhook-trigger/01961497-8bf5-4d5b-9ce2-013a513f5de0"
}) => {
    // State for all form fields
    const [formData, setFormData] = useState({
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        state: '',
        country: '',
        postal_code: '',
        project_image_url: '',
        consent: false,
    });

    // State for UI feedback
    const [uploadStatus, setUploadStatus] = useState('');
    const [formStatus, setFormStatus] = useState('');
    const [imagePreview, setImagePreview] = useState('');
    const [isUploading, setIsUploading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Refs for DOM elements that need direct manipulation
    const addressAutocompleteRef = useRef<HTMLInputElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const autocompleteInstance = useRef<any>(null);

    // Effect to load the Google Places API script
    useEffect(() => {
        const scriptId = 'google-places-api';
        if (document.getElementById(scriptId)) return; // Script already loaded

        const script = document.createElement('script');
        script.id = scriptId;
        script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_PLACES_API_KEY}&libraries=places&callback=initAutocomplete`;
        script.async = true;
        script.defer = true;
        (window as any).initAutocomplete = initAutocomplete; // Make it globally available for the callback
        document.head.appendChild(script);

        return () => {
            // Clean up the global callback function when the component unmounts
            delete (window as any).initAutocomplete;
        };
    }, []);

    // Function to initialize the autocomplete instance
    const initAutocomplete = () => {
        if (addressAutocompleteRef.current && !autocompleteInstance.current) {
            autocompleteInstance.current = new (window as any).google.maps.places.Autocomplete(
                addressAutocompleteRef.current,
                {
                    types: ['address'],
                    componentRestrictions: { 'country': ['US', 'CA'] },
                    fields: ['address_components', 'formatted_address'],
                }
            );
            autocompleteInstance.current.addListener('place_changed', fillInAddress);
        }
    };

    // Handler for when a place is selected from the dropdown
    const fillInAddress = () => {
        const place = autocompleteInstance.current.getPlace();
        const addressComponents = {
            address: '',
            city: '',
            state: '',
            country: '',
            postal_code: '',
        };
        let streetNumber = '';
        let route = '';

        place.address_components.forEach((component: any) => {
            const componentType = component.types[0];
            switch (componentType) {
                case "street_number": streetNumber = component.long_name; break;
                case "route": route = component.long_name; break;
                case "locality": addressComponents.city = component.long_name; break;
                case "administrative_area_level_1": addressComponents.state = component.short_name; break;
                case "country": addressComponents.country = component.long_name; break;
                case "postal_code": addressComponents.postal_code = component.long_name; break;
                default: break;
            }
        });

        addressComponents.address = `${streetNumber} ${route}`.trim();
        setFormData(prev => ({ ...prev, ...addressComponents }));
        // Also update the visible input's value directly
        if (addressAutocompleteRef.current) {
            addressAutocompleteRef.current.value = place.formatted_address;
        }
    };

    // Handler for standard text input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value,
        }));
    };

    // Handler for file selection (from click or drop)
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            uploadImage(file);
        }
    };

    // Function to upload the image to Cloudflare Worker
    const uploadImage = async (file: File) => {
        setIsUploading(true);
        setUploadStatus('Uploading image...');
        const uploadFormData = new FormData();
        uploadFormData.append('image', file);

        console.log('Uploading to:', CLOUDFLARE_WORKER_URL);
        console.log('Auth key:', CLOUDFLARE_AUTH_KEY);

        try {
            const response = await fetch(CLOUDFLARE_WORKER_URL, {
                method: 'POST',
                body: uploadFormData,
                headers: {
                    'Authorization': `Bearer ${CLOUDFLARE_AUTH_KEY}`,
                },
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', Object.fromEntries(response.headers.entries()));

            const result = await response.json();
            console.log('Response body:', result);

            if (result.success) {
                setFormData(prev => ({ ...prev, project_image_url: result.url }));
                setImagePreview(result.url);
                setUploadStatus('Image uploaded!');
            } else {
                throw new Error(result.error || 'Upload failed');
            }
        } catch (error) {
            console.error('Image upload failed:', error);
            setUploadStatus('Upload failed. Please try again.');
        } finally {
            setIsUploading(false);
        }
    };
    
    // Drag and drop handlers
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => e.preventDefault();
    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        const file = e.dataTransfer.files[0];
        if (file) {
            uploadImage(file);
        }
    };

    // Handler for form submission
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);
        setFormStatus('Submitting...');

        try {
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                setFormStatus('Thank you! Your estimate request has been sent.');
                // Reset form state after successful submission
                setFormData({
                    first_name: '', last_name: '', phone: '', email: '',
                    address: '', city: '', state: '', country: '', postal_code: '',
                    project_image_url: '', consent: false
                });
                setImagePreview('');
                setUploadStatus('');
                if (addressAutocompleteRef.current) addressAutocompleteRef.current.value = '';
            } else {
                throw new Error(`Server responded with status: ${response.status}`);
            }
        } catch (error) {
            console.error('Form submission failed:', error);
            setFormStatus('Submission failed. Please check your connection and try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="w-full max-w-md bg-blue-600 text-white rounded-2xl shadow-2xl p-8">
            <h2 className="text-2xl font-bold text-center mb-6">{title}</h2>
            <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                    <input 
                        type="text" 
                        name="first_name" 
                        value={formData.first_name} 
                        onChange={handleInputChange} 
                        placeholder="First Name" 
                        className="w-full p-3 rounded-lg text-gray-800" 
                        required 
                    />
                    <input 
                        type="text" 
                        name="last_name" 
                        value={formData.last_name} 
                        onChange={handleInputChange} 
                        placeholder="Last Name" 
                        className="w-full p-3 rounded-lg text-gray-800" 
                        required 
                    />
                    <input 
                        type="tel" 
                        name="phone" 
                        value={formData.phone} 
                        onChange={handleInputChange} 
                        placeholder="Phone*" 
                        className="w-full p-3 rounded-lg text-gray-800" 
                        required 
                    />
                    <input 
                        type="email" 
                        name="email" 
                        value={formData.email} 
                        onChange={handleInputChange} 
                        placeholder="Email*" 
                        className="w-full p-3 rounded-lg text-gray-800" 
                        required 
                    />
                    <input 
                        ref={addressAutocompleteRef} 
                        type="text" 
                        placeholder="Search address" 
                        className="w-full p-3 rounded-lg text-gray-800" 
                    />

                    <div
                        className="bg-blue-500 border-2 border-dashed border-blue-300 rounded-lg p-6 text-center cursor-pointer"
                        onClick={() => fileInputRef.current?.click()}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                    >
                        {imagePreview ? (
                            <img src={imagePreview} alt="Project preview" className="mx-auto h-16 w-16 object-cover rounded-md" />
                        ) : (
                            <>
                                <UploadIcon />
                                <label htmlFor="fileUpload" className="font-medium text-white hover:text-blue-100 cursor-pointer mt-2 block">
                                    Image Upload of Project
                                </label>
                                <p className="text-xs text-blue-200 mt-1">PNG, JPG, GIF up to 10MB</p>
                            </>
                        )}
                        <input 
                            ref={fileInputRef} 
                            id="fileUpload" 
                            type="file" 
                            accept="image/*" 
                            onChange={handleFileChange} 
                            className="hidden" 
                        />
                    </div>

                    {uploadStatus && (
                         <div className="text-center text-sm flex items-center justify-center gap-2">
                            {isUploading && <Loader />}
                            <span className={uploadStatus.includes('failed') ? 'text-red-300' : 'text-green-200'}>{uploadStatus}</span>
                        </div>
                    )}

                    <div className="flex items-start space-x-3">
                        <input 
                            id="consent" 
                            name="consent" 
                            type="checkbox" 
                            checked={formData.consent} 
                            onChange={handleInputChange} 
                            className="h-5 w-5 rounded mt-1" 
                            required 
                        />
                        <label htmlFor="consent" className="text-xs text-blue-100">
                            I agree to receive text message updates from Deckora. Msg frequency varies (e.g., up to 4/month). Msg & data rates may apply. Reply STOP to unsubscribe at any time. View privacy & terms.
                        </label>
                    </div>
                </div>

                <button 
                    type="submit" 
                    disabled={isSubmitting} 
                    className="w-full bg-gray-800 hover:bg-gray-900 text-white font-bold p-4 rounded-lg mt-6 transition duration-300 disabled:bg-gray-500"
                >
                    {isSubmitting ? 'Submitting...' : 'Get Started'}
                </button>

                {formStatus && (
                    <div className={`text-center mt-4 font-medium ${formStatus.includes('failed') ? 'text-red-300' : 'text-green-200'}`}>
                        {formStatus}
                    </div>
                )}
            </form>
        </div>
    );
};

export default CustomForm;
